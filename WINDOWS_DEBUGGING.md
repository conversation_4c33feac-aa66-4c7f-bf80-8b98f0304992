# Windows Sidecar Debugging Guide

This guide helps debug issues with the `dweb_open` command and sidecar binary execution on Windows.

## Common Issues

### 1. No Response from `dweb_open` Command
**Symptoms**: The `dweb_open` command appears to execute but produces no output or response.

**Causes**:
- Windows subsystem setting prevents console output in release builds
- Sidecar binary not found or not executable
- Windows security restrictions blocking sidecar execution
- Missing shell permissions in Tauri capabilities

### 2. No Output from `colony-dweb.exe`
**Symptoms**: Running `colony-dweb.exe` directly produces no visible output.

**Causes**:
- Console output suppressed by Windows subsystem setting
- Binary may be running but output not captured
- Process may be terminating immediately due to errors

## Debugging Steps

### Step 1: Enable Console Output
To see console output and debug information on Windows:

```bash
# For development with console output
npm run tauri:dev:console

# For building with console output
npm run tauri:build:console
```

This enables the `console` feature flag which allows console output on Windows.

### Step 2: Use the Debug Tool
1. Open the Colony application
2. Navigate to Configuration page
3. Scroll down to the "Debug Information (Windows Sidecar)" section
4. Click "Check Sidecar Status" button
5. Review the debug output for:
   - Platform information
   - Sidecar binary detection
   - Binary path resolution
   - Dweb serve status

### Step 3: Manual Binary Testing
Test the sidecar binary manually:

```cmd
# Navigate to the binary location (usually in target/binaries)
cd target\binaries

# Test the binary directly
colony-dweb-x86_64-pc-windows-msvc.exe --help

# Test with specific commands
colony-dweb-x86_64-pc-windows-msvc.exe open <address>
```

### Step 4: Check Binary Permissions
Ensure the sidecar binary has proper execution permissions:
- Right-click on `colony-dweb-x86_64-pc-windows-msvc.exe`
- Select Properties
- Check if Windows is blocking the file (Unblock if necessary)
- Verify the file is not corrupted

### Step 5: Verify Tauri Configuration
Check that the following are properly configured:

1. **Sidecar Binary in tauri.conf.json**:
   ```json
   "bundle": {
     "externalBin": ["../target/binaries/colony-dweb"]
   }
   ```

2. **Shell Permissions in capabilities/default.json**:
   ```json
   "permissions": [
     "shell:default",
     "shell:allow-execute",
     "shell:allow-spawn"
   ]
   ```

3. **Sidecar Execute Permission**:
   ```json
   {
     "identifier": "shell:allow-execute",
     "allow": [
       {
         "name": "colony-dweb",
         "sidecar": true
       }
     ]
   }
   ```

## Enhanced Logging

The application now includes enhanced logging for Windows sidecar execution:

- All sidecar operations are logged with detailed information
- Windows-specific checks are performed before execution
- Binary path resolution is logged
- Process spawn success/failure is tracked
- stdout/stderr from sidecar processes are captured and logged

## Log Locations

Logs are stored in the application data directory:
- Windows: `%APPDATA%\Colony\logs\`
- Look for files with current date for recent logs

## Common Solutions

### Solution 1: Rebuild with Console Support
```bash
npm run tauri:build:console
```

### Solution 2: Re-fetch Binaries
```bash
cd src-tauri/binaries
./fetch_binaries.sh
```

### Solution 3: Manual Binary Placement
If automatic binary fetching fails:
1. Download the appropriate `dweb-amd64.exe` from the dweb releases
2. Rename it to `colony-dweb-x86_64-pc-windows-msvc.exe`
3. Place it in `target/binaries/`
4. Ensure it has execute permissions

### Solution 4: Windows Defender/Antivirus
Some antivirus software may block unsigned executables:
1. Add the Colony application folder to antivirus exclusions
2. Add the `target/binaries` folder to exclusions
3. Temporarily disable real-time protection for testing

## Getting Help

If issues persist:
1. Run the debug tool and capture the output
2. Check the application logs
3. Test the binary manually from command line
4. Report the issue with all debug information collected
